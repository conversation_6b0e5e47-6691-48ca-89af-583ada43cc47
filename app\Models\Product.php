<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Product extends Model
{
    use HasFactory;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'category',
        'price',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'price' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope a query to filter by category
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * Scope a query to filter by maximum price
     */
    public function scopeMaxPrice($query, $maxPrice)
    {
        return $query->where('price', '<=', $maxPrice);
    }

    /**
     * Scope a query to sort by latest (newest first)
     */
    public function scopeSortByLatest($query)
    {
        return $query->orderBy('created_at', 'desc');
    }

    /**
     * Scope a query to sort by cheapest price
     */
    public function scopeSortByCheapest($query)
    {
        return $query->orderBy('price', 'asc');
    }

    /**
     * Scope a query to sort by most expensive price
     */
    public function scopeSortByMostExpensive($query)
    {
        return $query->orderBy('price', 'desc');
    }
}
