<?php

namespace Database\Factories;

use App\Models\Catalog;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Catalog>
 */
class CatalogFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Catalog::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'id_produk' => $this->faker->unique()->regexify('[A-Z]{2}[0-9]{4}'),
            'nama' => $this->faker->words(2, true),
            'stock' => $this->faker->numberBetween(1, 100),
            'keterangan' => $this->faker->sentence(5),
            'harga' => $this->faker->numberBetween(50000, 1000000),
            'gambar' => null,
        ];
    }

    /**
     * Create a product with specific category and price for testing
     */
    public function elektronik(): static
    {
        return $this->state(fn (array $attributes) => [
            'keterangan' => 'Produk elektronik berkualitas tinggi',
        ]);
    }

    /**
     * Create a product with specific category and price for testing
     */
    public function pakaian(): static
    {
        return $this->state(fn (array $attributes) => [
            'keterangan' => 'Pakaian berkualitas dan nyaman',
        ]);
    }

    /**
     * Create a product with low price
     */
    public function murah(): static
    {
        return $this->state(fn (array $attributes) => [
            'harga' => $this->faker->numberBetween(50000, 200000),
        ]);
    }

    /**
     * Create a product with high price
     */
    public function mahal(): static
    {
        return $this->state(fn (array $attributes) => [
            'harga' => $this->faker->numberBetween(800000, 1000000),
        ]);
    }
}
