<?php

namespace Tests\Feature;

use App\Models\Product;
use Illuminate\Foundation\Testing\DatabaseTransactions;
use Tests\TestCase;

class ProductCatalogTest extends TestCase
{
    use DatabaseTransactions;

    /**
     * Set up test data before each test
     */
    protected function setUp(): void
    {
        parent::setUp();

        // Clean up any existing test products
        Product::where('name', 'LIKE', '%Test%')->delete();
        Product::whereIn('name', ['Mouse Gaming', 'T-Shirt Polos', 'Keyboard Mechanical', 'Kemeja Flanel'])->delete();

        // Create test products with specific data as requested
        $mouseGaming = new Product([
            'name' => 'Mouse Gaming',
            'category' => 'Elektronik',
            'price' => 450000,
        ]);
        $mouseGaming->created_at = '2023-01-10 00:00:00';
        $mouseGaming->updated_at = '2023-01-10 00:00:00';
        $mouseGaming->save();

        $tshirt = new Product([
            'name' => 'T-Shirt Polos',
            'category' => 'Pakaian',
            'price' => 150000,
        ]);
        $tshirt->created_at = '2023-03-15 00:00:00';
        $tshirt->updated_at = '2023-03-15 00:00:00';
        $tshirt->save();

        $keyboard = new Product([
            'name' => 'Keyboard Mechanical',
            'category' => 'Elektronik',
            'price' => 950000,
        ]);
        $keyboard->created_at = '2023-05-20 00:00:00';
        $keyboard->updated_at = '2023-05-20 00:00:00';
        $keyboard->save();

        $kemeja = new Product([
            'name' => 'Kemeja Flanel',
            'category' => 'Pakaian',
            'price' => 350000,
        ]);
        $kemeja->created_at = '2023-02-05 00:00:00';
        $kemeja->updated_at = '2023-02-05 00:00:00';
        $kemeja->save();
    }

    /**
     * Test Case 1: Kombinasi Kategori Pakaian diurutkan Termurah
     * Request: GET /products?category=Pakaian&sort=termurah
     * Ekspektasi: Halaman menampilkan "T-Shirt Polos" lalu "Kemeja Flanel". Produk elektronik tidak boleh tampil.
     */
    public function test_filter_pakaian_sort_termurah(): void
    {
        $response = $this->get('/products?category=Pakaian&sort=termurah');

        $response->assertStatus(200);
        
        // Memastikan produk pakaian muncul dengan urutan harga termurah
        $response->assertSee('T-Shirt Polos');
        $response->assertSee('Kemeja Flanel');
        $response->assertSeeInOrder(['T-Shirt Polos', 'Kemeja Flanel']);
        
        // Memastikan produk elektronik tidak muncul
        $response->assertDontSee('Mouse Gaming');
        $response->assertDontSee('Keyboard Mechanical');
    }

    /**
     * Test Case 2: Kombinasi Harga di bawah 500rb diurutkan Terbaru
     * Request: GET /products?max_price=500000&sort=terbaru
     * Ekspektasi: Halaman menampilkan "T-Shirt Polos", lalu "Kemeja Flanel", lalu "Mouse Gaming". Keyboard tidak boleh tampil.
     */
    public function test_filter_max_price_500k_sort_terbaru(): void
    {
        $response = $this->get('/products?max_price=500000&sort=terbaru');

        $response->assertStatus(200);
        
        // Memastikan produk dengan harga <= 500k muncul dengan urutan terbaru
        $response->assertSee('T-Shirt Polos');
        $response->assertSee('Kemeja Flanel');
        $response->assertSee('Mouse Gaming');
        $response->assertSeeInOrder(['T-Shirt Polos', 'Kemeja Flanel', 'Mouse Gaming']);
        
        // Memastikan keyboard (harga 950k) tidak muncul
        $response->assertDontSee('Keyboard Mechanical');
    }

    /**
     * Test Case 3: Kombinasi Kategori Elektronik diurutkan Termahal
     * Request: GET /products?category=Elektronik&sort=termahal
     * Ekspektasi: Halaman menampilkan "Keyboard Mechanical" lalu "Mouse Gaming". Produk pakaian tidak boleh tampil.
     */
    public function test_filter_elektronik_sort_termahal(): void
    {
        $response = $this->get('/products?category=Elektronik&sort=termahal');

        $response->assertStatus(200);
        
        // Memastikan produk elektronik muncul dengan urutan harga termahal
        $response->assertSee('Keyboard Mechanical');
        $response->assertSee('Mouse Gaming');
        $response->assertSeeInOrder(['Keyboard Mechanical', 'Mouse Gaming']);
        
        // Memastikan produk pakaian tidak muncul
        $response->assertDontSee('T-Shirt Polos');
        $response->assertDontSee('Kemeja Flanel');
    }

    /**
     * Test Case 4: Kombinasi Harga di bawah 200k diurutkan Termurah
     * Request: GET /products?max_price=200000&sort=termurah
     * Ekspektasi: Hanya "T-Shirt Polos" yang muncul.
     */
    public function test_filter_max_price_200k_sort_termurah(): void
    {
        $response = $this->get('/products?max_price=200000&sort=termurah');

        $response->assertStatus(200);
        
        // Memastikan hanya T-Shirt Polos yang muncul (harga 150k)
        $response->assertSee('T-Shirt Polos');
        
        // Memastikan produk lain tidak muncul
        $response->assertDontSee('Mouse Gaming');
        $response->assertDontSee('Keyboard Mechanical');
        $response->assertDontSee('Kemeja Flanel');
    }

    /**
     * Test Case 5: Kombinasi Kategori Pakaian dengan Harga Maksimal 300k
     * Request: GET /products?category=Pakaian&max_price=300000
     * Ekspektasi: Hanya "T-Shirt Polos" yang muncul.
     */
    public function test_filter_pakaian_max_price_300k(): void
    {
        $response = $this->get('/products?category=Pakaian&max_price=300000');

        $response->assertStatus(200);
        
        // Memastikan hanya T-Shirt Polos yang muncul (pakaian dengan harga <= 300k)
        $response->assertSee('T-Shirt Polos');
        
        // Memastikan produk lain tidak muncul
        $response->assertDontSee('Kemeja Flanel'); // Harga 350k
        $response->assertDontSee('Mouse Gaming');
        $response->assertDontSee('Keyboard Mechanical');
    }

    /**
     * Test Case 6: Semua Produk diurutkan Terbaru (tanpa filter)
     * Request: GET /products?sort=terbaru
     * Ekspektasi: Semua produk muncul dengan urutan: Keyboard, T-Shirt, Kemeja, Mouse
     */
    public function test_all_products_sort_terbaru(): void
    {
        $response = $this->get('/products?sort=terbaru');

        $response->assertStatus(200);
        
        // Memastikan semua produk muncul dengan urutan created_at descending
        $response->assertSee('Keyboard Mechanical');
        $response->assertSee('T-Shirt Polos');
        $response->assertSee('Kemeja Flanel');
        $response->assertSee('Mouse Gaming');
        $response->assertSeeInOrder(['Keyboard Mechanical', 'T-Shirt Polos', 'Kemeja Flanel', 'Mouse Gaming']);
    }

    /**
     * Test Case 7: Semua Produk diurutkan Termurah (tanpa filter)
     * Request: GET /products?sort=termurah
     * Ekspektasi: Semua produk muncul dengan urutan: T-Shirt, Kemeja, Mouse, Keyboard
     */
    public function test_all_products_sort_termurah(): void
    {
        $response = $this->get('/products?sort=termurah');

        $response->assertStatus(200);
        
        // Memastikan semua produk muncul dengan urutan harga ascending
        $response->assertSee('T-Shirt Polos');
        $response->assertSee('Kemeja Flanel');
        $response->assertSee('Mouse Gaming');
        $response->assertSee('Keyboard Mechanical');
        $response->assertSeeInOrder(['T-Shirt Polos', 'Kemeja Flanel', 'Mouse Gaming', 'Keyboard Mechanical']);
    }

    /**
     * Test Case 8: Kombinasi Kategori Elektronik dengan Harga Maksimal 500k diurutkan Termurah
     * Request: GET /products?category=Elektronik&max_price=500000&sort=termurah
     * Ekspektasi: Hanya "Mouse Gaming" yang muncul.
     */
    public function test_filter_elektronik_max_price_500k_sort_termurah(): void
    {
        $response = $this->get('/products?category=Elektronik&max_price=500000&sort=termurah');

        $response->assertStatus(200);
        
        // Memastikan hanya Mouse Gaming yang muncul (elektronik dengan harga <= 500k)
        $response->assertSee('Mouse Gaming');
        
        // Memastikan produk lain tidak muncul
        $response->assertDontSee('Keyboard Mechanical'); // Harga 950k
        $response->assertDontSee('T-Shirt Polos'); // Bukan elektronik
        $response->assertDontSee('Kemeja Flanel'); // Bukan elektronik
    }

    /**
     * Test Case 9: Tanpa Filter dan Tanpa Sorting
     * Request: GET /products
     * Ekspektasi: Semua produk muncul tanpa urutan khusus
     */
    public function test_no_filter_no_sorting(): void
    {
        $response = $this->get('/products');

        $response->assertStatus(200);
        
        // Memastikan semua produk muncul
        $response->assertSee('Mouse Gaming');
        $response->assertSee('T-Shirt Polos');
        $response->assertSee('Keyboard Mechanical');
        $response->assertSee('Kemeja Flanel');
    }

    /**
     * Test Case 10: Filter dengan Kategori yang Tidak Ada
     * Request: GET /products?category=Makanan
     * Ekspektasi: Tidak ada produk yang muncul
     */
    public function test_filter_nonexistent_category(): void
    {
        $response = $this->get('/products?category=Makanan');

        $response->assertStatus(200);
        
        // Memastikan tidak ada produk yang muncul
        $response->assertDontSee('Mouse Gaming');
        $response->assertDontSee('T-Shirt Polos');
        $response->assertDontSee('Keyboard Mechanical');
        $response->assertDontSee('Kemeja Flanel');
    }
}
