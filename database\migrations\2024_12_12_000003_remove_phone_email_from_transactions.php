<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (Schema::hasTable('transactions')) {
            Schema::table('transactions', function (Blueprint $table) {
                if (Schema::hasColumn('transactions', 'customer_phone')) {
                    $table->dropColumn('customer_phone');
                }
                if (Schema::hasColumn('transactions', 'customer_email')) {
                    $table->dropColumn('customer_email');
                }
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        if (Schema::hasTable('transactions')) {
            Schema::table('transactions', function (Blueprint $table) {
                if (!Schema::hasColumn('transactions', 'customer_phone')) {
                    $table->string('customer_phone', 20)->nullable()->after('customer_name');
                }
                if (!Schema::hasColumn('transactions', 'customer_email')) {
                    $table->string('customer_email')->nullable()->after('customer_phone');
                }
            });
        }
    }
};
