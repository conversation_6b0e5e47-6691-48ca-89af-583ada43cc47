<?php

namespace Database\Factories;

use App\Models\Product;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Product>
 */
class ProductFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Product::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => $this->faker->words(2, true),
            'category' => $this->faker->randomElement(['Elektronik', 'Pakaian', 'Makanan', 'Minuman']),
            'price' => $this->faker->numberBetween(50000, 1000000),
        ];
    }

    /**
     * Create a product in Elektronik category
     */
    public function elektronik(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'Elektronik',
        ]);
    }

    /**
     * Create a product in Pakaian category
     */
    public function pakaian(): static
    {
        return $this->state(fn (array $attributes) => [
            'category' => 'Pakaian',
        ]);
    }

    /**
     * Create a product with specific price
     */
    public function withPrice(int $price): static
    {
        return $this->state(fn (array $attributes) => [
            'price' => $price,
        ]);
    }

    /**
     * Create a product with specific name
     */
    public function withName(string $name): static
    {
        return $this->state(fn (array $attributes) => [
            'name' => $name,
        ]);
    }

    /**
     * Create a product with specific created_at date
     */
    public function createdAt($date): static
    {
        return $this->state(fn (array $attributes) => [
            'created_at' => $date,
        ]);
    }
}
