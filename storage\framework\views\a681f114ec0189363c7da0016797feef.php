<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Product Catalog</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .product { border: 1px solid #ddd; padding: 10px; margin: 10px 0; }
        .product h3 { margin: 0 0 10px 0; }
        .product p { margin: 5px 0; }
        .filters { background: #f5f5f5; padding: 15px; margin-bottom: 20px; }
        .filters form { display: flex; gap: 15px; align-items: center; }
        .filters input, .filters select { padding: 5px; }
        .filters button { padding: 5px 15px; background: #007cba; color: white; border: none; cursor: pointer; }
    </style>
</head>
<body>
    <h1>Product Catalog</h1>
    
    <div class="filters">
        <form method="GET" action="<?php echo e(route('products.index')); ?>">
            <label>
                Category:
                <select name="category">
                    <option value="">All Categories</option>
                    <option value="Elektronik" <?php echo e(request('category') == 'Elektronik' ? 'selected' : ''); ?>>Elektronik</option>
                    <option value="Pakaian" <?php echo e(request('category') == 'Pakaian' ? 'selected' : ''); ?>>Pakaian</option>
                </select>
            </label>
            
            <label>
                Max Price:
                <input type="number" name="max_price" value="<?php echo e(request('max_price')); ?>" placeholder="Maximum price">
            </label>
            
            <label>
                Sort:
                <select name="sort">
                    <option value="">No sorting</option>
                    <option value="terbaru" <?php echo e(request('sort') == 'terbaru' ? 'selected' : ''); ?>>Terbaru</option>
                    <option value="termurah" <?php echo e(request('sort') == 'termurah' ? 'selected' : ''); ?>>Termurah</option>
                    <option value="termahal" <?php echo e(request('sort') == 'termahal' ? 'selected' : ''); ?>>Termahal</option>
                </select>
            </label>
            
            <button type="submit">Filter</button>
        </form>
    </div>
    
    <div class="products">
        <?php $__empty_1 = true; $__currentLoopData = $products; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $product): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
            <div class="product">
                <h3><?php echo e($product->name); ?></h3>
                <p><strong>Category:</strong> <?php echo e($product->category); ?></p>
                <p><strong>Price:</strong> Rp <?php echo e(number_format($product->price, 0, ',', '.')); ?></p>
                <p><strong>Created:</strong> <?php echo e($product->created_at->format('Y-m-d H:i:s')); ?></p>
            </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
            <p>No products found.</p>
        <?php endif; ?>
    </div>
</body>
</html>
<?php /**PATH D:\KODE PROGRAM\ADPL PPL\UAS\IndoFresh\resources\views/products/index.blade.php ENDPATH**/ ?>